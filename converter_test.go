package main

import (
	"reflect"
	"testing"
)

func TestOpenAIToJetbrainsMessages_TextOnly(t *testing.T) {
	messages := []ChatMessage{
		{
			Role:    "user",
			Content: "Hello, world!",
		},
	}

	result := openAIToJetbrainsMessages(messages)

	if len(result) != 1 {
		t.Fatalf("Expected 1 message, got %d", len(result))
	}

	if result[0].Type != "user_message" {
		t.<PERSON><PERSON>("Expected type 'user_message', got '%s'", result[0].Type)
	}

	if result[0].Content != "Hello, world!" {
		t.<PERSON><PERSON><PERSON>("Expected content 'Hello, world!', got '%s'", result[0].Content)
	}

	if len(result[0].Images) != 0 {
		t.<PERSON><PERSON><PERSON>("Expected no images, got %d", len(result[0].Images))
	}
}

func TestOpenAIToJetbrainsMessages_WithImages(t *testing.T) {
	messages := []ChatMessage{
		{
			Role: "user",
			Content: []interface{}{
				map[string]interface{}{
					"type": "text",
					"text": "What's in this image?",
				},
				map[string]interface{}{
					"type": "image_url",
					"image_url": map[string]interface{}{
						"url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==",
					},
				},
			},
		},
	}

	result := openAIToJetbrainsMessages(messages)

	if len(result) != 1 {
		t.Fatalf("Expected 1 message, got %d", len(result))
	}

	if result[0].Type != "user_message" {
		t.Errorf("Expected type 'user_message', got '%s'", result[0].Type)
	}

	if result[0].Content != "What's in this image?" {
		t.Errorf("Expected content 'What's in this image?', got '%s'", result[0].Content)
	}

	if len(result[0].Images) != 1 {
		t.Fatalf("Expected 1 image, got %d", len(result[0].Images))
	}

	expectedImage := JetbrainsImage{
		Type: "image_url",
		ImageUrl: JetbrainsImageUrl{
			Url: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==",
		},
	}

	if !reflect.DeepEqual(result[0].Images[0], expectedImage) {
		t.Errorf("Image mismatch. Expected %+v, got %+v", expectedImage, result[0].Images[0])
	}
}

func TestOpenAIToJetbrainsMessages_MultipleImages(t *testing.T) {
	messages := []ChatMessage{
		{
			Role: "user",
			Content: []interface{}{
				map[string]interface{}{
					"type": "text",
					"text": "Compare these images:",
				},
				map[string]interface{}{
					"type": "image_url",
					"image_url": map[string]interface{}{
						"url": "data:image/png;base64,image1",
					},
				},
				map[string]interface{}{
					"type": "image_url",
					"image_url": map[string]interface{}{
						"url": "data:image/png;base64,image2",
					},
				},
			},
		},
	}

	result := openAIToJetbrainsMessages(messages)

	if len(result) != 1 {
		t.Fatalf("Expected 1 message, got %d", len(result))
	}

	if result[0].Content != "Compare these images:" {
		t.Errorf("Expected content 'Compare these images:', got '%s'", result[0].Content)
	}

	if len(result[0].Images) != 2 {
		t.Fatalf("Expected 2 images, got %d", len(result[0].Images))
	}

	if result[0].Images[0].ImageUrl.Url != "data:image/png;base64,image1" {
		t.Errorf("First image URL mismatch")
	}

	if result[0].Images[1].ImageUrl.Url != "data:image/png;base64,image2" {
		t.Errorf("Second image URL mismatch")
	}
}

func TestOpenAIToJetbrainsMessages_WithToolCalls(t *testing.T) {
	messages := []ChatMessage{
		{
			Role:    "assistant",
			Content: "I'll help you with that.",
			ToolCalls: []ToolCall{
				{
					ID:   "call_123",
					Type: "function",
					Function: Function{
						Name:      "get_weather",
						Arguments: `{"location": "New York"}`,
					},
				},
			},
		},
	}

	result := openAIToJetbrainsMessages(messages)

	if len(result) != 1 {
		t.Fatalf("Expected 1 message, got %d", len(result))
	}

	if result[0].Type != "assistant_message" {
		t.Errorf("Expected type 'assistant_message', got '%s'", result[0].Type)
	}

	if result[0].FunctionCall == nil {
		t.Fatalf("Expected function call, got nil")
	}

	if result[0].FunctionCall.FunctionName != "get_weather" {
		t.Errorf("Expected function name 'get_weather', got '%s'", result[0].FunctionCall.FunctionName)
	}
}
