# Git 相关
.git
.gitignore
*.md
!CLAUDE.md

# 构建产物
jetbrainsai2api
*.exe
*.dll
*.so
*.dylib

# 临时文件
*.tmp
*.log
*.swp
*.swo
*~

# IDE 文件
.vscode/
.idea/
*.iml

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 环境和配置文件
.env
.env.local
.env.*.local

# 运行时数据
data/
logs/
stats.json

# 测试相关
coverage.txt
*.test

# Docker 相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 其他
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*