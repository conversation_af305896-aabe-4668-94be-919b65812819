package main

import (
	"log"
	"strings"

	"github.com/bytedance/sonic"
)

// openAIToJetbrainsMessages converts OpenAI chat messages to JetBrains format
func openAIToJetbrainsMessages(messages []ChatMessage) []JetbrainsMessage {
	toolIDToFuncNameMap := make(map[string]string)
	for _, msg := range messages {
		if msg.Role == "assistant" && msg.ToolCalls != nil {
			for _, tc := range msg.ToolCalls {
				if tc.ID != "" && tc.Function.Name != "" {
					toolIDToFuncNameMap[tc.ID] = tc.Function.Name
				}
			}
		}
	}

	var jetbrainsMessages []JetbrainsMessage
	for _, msg := range messages {
		textContent := extractTextContent(msg.Content)

		switch msg.Role {
		case "user", "system":
			jetbrainsMessages = append(jetbrainsMessages, JetbrainsMessage{
				Type:    msg.Role + "_message",
				Content: textContent,
			})
		case "assistant":
			if len(msg.ToolCalls) > 0 {
				toolCall := msg.ToolCalls[0]

				// 尝试解析参数，如果是一个 JSON 字符串，就解码它以获取原始的参数对象
				var argsMap map[string]any
				if err := sonic.UnmarshalString(toolCall.Function.Arguments, &argsMap); err == nil {
					// 如果成功解码，重新编码以确保它是一个干净的 JSON
					cleanArgs, _ := sonic.Marshal(argsMap)
					toolCall.Function.Arguments = string(cleanArgs)
				}
				// 如果解码失败，我们假定它已经是我们想要的格式

				jetbrainsMessages = append(jetbrainsMessages, JetbrainsMessage{
					Type:    "assistant_message",
					Content: textContent,
					FunctionCall: &JetbrainsFunctionCall{
						FunctionName: toolCall.Function.Name,
						Content:      toolCall.Function.Arguments,
					},
				})
			} else {
				jetbrainsMessages = append(jetbrainsMessages, JetbrainsMessage{
					Type:    "assistant_message",
					Content: textContent,
				})
			}
		case "tool":
			functionName := toolIDToFuncNameMap[msg.ToolCallID]
			if functionName != "" {
				jetbrainsMessages = append(jetbrainsMessages, JetbrainsMessage{
					Type:         "function_message",
					Content:      textContent,
					FunctionName: functionName,
				})
			} else {
				log.Printf("Warning: Cannot find function name for tool_call_id %s", msg.ToolCallID)
			}
		default:
			jetbrainsMessages = append(jetbrainsMessages, JetbrainsMessage{
				Type:    "user_message",
				Content: textContent,
			})
		}
	}
	return jetbrainsMessages
}

// convertOpenAIToJetbrains converts OpenAI format messages to JetBrains format
func convertOpenAIToJetbrains(messages []ChatMessage) []JetbrainsMessage {
	var jetbrainsMessages []JetbrainsMessage

	for _, msg := range messages {
		jetbrainsMsg := JetbrainsMessage{
			Role: msg.Role,
		}

		// Handle different content types
		switch content := msg.Content.(type) {
		case string:
			jetbrainsMsg.Content = content
		case []interface{}:
			// Handle multimodal content (text + images)
			var textParts []string
			var images []JetbrainsImage

			for _, part := range content {
				if partMap, ok := part.(map[string]interface{}); ok {
					if partType, exists := partMap["type"]; exists {
						switch partType {
						case "text":
							if text, ok := partMap["text"].(string); ok {
								textParts = append(textParts, text)
							}
						case "image_url":
							if imageUrl, ok := partMap["image_url"].(map[string]interface{}); ok {
								if url, ok := imageUrl["url"].(string); ok {
									images = append(images, JetbrainsImage{
										Type: "image_url",
										ImageUrl: JetbrainsImageUrl{
											Url: url,
										},
									})
								}
							}
						}
					}
				}
			}

			// Combine text parts
			if len(textParts) > 0 {
				jetbrainsMsg.Content = strings.Join(textParts, " ")
			}

			// Add images if present
			if len(images) > 0 {
				jetbrainsMsg.Images = images
			}
		}

		// Handle tool calls
		if len(msg.ToolCalls) > 0 {
			jetbrainsMsg.ToolCalls = msg.ToolCalls
		}

		// Handle tool call ID
		if msg.ToolCallID != "" {
			jetbrainsMsg.ToolCallID = msg.ToolCallID
		}

		jetbrainsMessages = append(jetbrainsMessages, jetbrainsMsg)
	}

	return jetbrainsMessages
}
