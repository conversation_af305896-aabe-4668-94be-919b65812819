package main

import (
	"log"
	"strings"

	"github.com/bytedance/sonic"
)

// openAIToJetbrainsMessages converts OpenAI chat messages to JetBrains format with image support
func openAIToJetbrainsMessages(messages []ChatMessage) []JetbrainsMessage {
	toolIDToFuncNameMap := make(map[string]string)
	for _, msg := range messages {
		if msg.Role == "assistant" && msg.ToolCalls != nil {
			for _, tc := range msg.ToolCalls {
				if tc.ID != "" && tc.Function.Name != "" {
					toolIDToFuncNameMap[tc.ID] = tc.Function.Name
				}
			}
		}
	}

	var jetbrainsMessages []JetbrainsMessage
	for _, msg := range messages {
		// Extract text content and images
		var textContent string
		var images []JetbrainsImage

		// Handle different content types (text and images)
		switch content := msg.Content.(type) {
		case string:
			textContent = content
		case []interface{}:
			// Handle multimodal content (text + images)
			var textParts []string

			for _, part := range content {
				if partMap, ok := part.(map[string]interface{}); ok {
					if partType, exists := partMap["type"]; exists {
						switch partType {
						case "text":
							if text, ok := partMap["text"].(string); ok {
								textParts = append(textParts, text)
							}
						case "image_url":
							if imageUrl, ok := partMap["image_url"].(map[string]interface{}); ok {
								if url, ok := imageUrl["url"].(string); ok {
									images = append(images, JetbrainsImage{
										Type: "image_url",
										ImageUrl: JetbrainsImageUrl{
											Url: url,
										},
									})
								}
							}
						}
					}
				}
			}

			// Combine text parts
			if len(textParts) > 0 {
				textContent = strings.Join(textParts, " ")
			}
		default:
			// Fallback to extractTextContent for other types
			textContent = extractTextContent(msg.Content)
		}

		// Create JetBrains message based on role
		switch msg.Role {
		case "user", "system":
			jetbrainsMsg := JetbrainsMessage{
				Type:    msg.Role + "_message",
				Content: textContent,
			}
			// Add images if present
			if len(images) > 0 {
				jetbrainsMsg.Images = images
			}
			jetbrainsMessages = append(jetbrainsMessages, jetbrainsMsg)
		case "assistant":
			if len(msg.ToolCalls) > 0 {
				toolCall := msg.ToolCalls[0]

				// 尝试解析参数，如果是一个 JSON 字符串，就解码它以获取原始的参数对象
				var argsMap map[string]any
				if err := sonic.UnmarshalString(toolCall.Function.Arguments, &argsMap); err == nil {
					// 如果成功解码，重新编码以确保它是一个干净的 JSON
					cleanArgs, _ := sonic.Marshal(argsMap)
					toolCall.Function.Arguments = string(cleanArgs)
				}
				// 如果解码失败，我们假定它已经是我们想要的格式

				jetbrainsMsg := JetbrainsMessage{
					Type:    "assistant_message",
					Content: textContent,
					FunctionCall: &JetbrainsFunctionCall{
						FunctionName: toolCall.Function.Name,
						Content:      toolCall.Function.Arguments,
					},
				}
				// Add images if present (though unlikely for assistant messages with tool calls)
				if len(images) > 0 {
					jetbrainsMsg.Images = images
				}
				jetbrainsMessages = append(jetbrainsMessages, jetbrainsMsg)
			} else {
				jetbrainsMsg := JetbrainsMessage{
					Type:    "assistant_message",
					Content: textContent,
				}
				// Add images if present
				if len(images) > 0 {
					jetbrainsMsg.Images = images
				}
				jetbrainsMessages = append(jetbrainsMessages, jetbrainsMsg)
			}
		case "tool":
			functionName := toolIDToFuncNameMap[msg.ToolCallID]
			if functionName != "" {
				jetbrainsMsg := JetbrainsMessage{
					Type:         "function_message",
					Content:      textContent,
					FunctionName: functionName,
				}
				// Add images if present (though unlikely for tool messages)
				if len(images) > 0 {
					jetbrainsMsg.Images = images
				}
				jetbrainsMessages = append(jetbrainsMessages, jetbrainsMsg)
			} else {
				log.Printf("Warning: Cannot find function name for tool_call_id %s", msg.ToolCallID)
			}
		default:
			jetbrainsMsg := JetbrainsMessage{
				Type:    "user_message",
				Content: textContent,
			}
			// Add images if present
			if len(images) > 0 {
				jetbrainsMsg.Images = images
			}
			jetbrainsMessages = append(jetbrainsMessages, jetbrainsMsg)
		}
	}
	return jetbrainsMessages
}
