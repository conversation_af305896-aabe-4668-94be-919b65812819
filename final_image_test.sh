#!/bin/bash

# Final test script for image support verification

API_URL="http://localhost:7860/v1/chat/completions"
API_KEY="testofli"

echo "=== Testing Image Support in /v1/chat/completions ==="
echo

# Test 1: Simple text message (baseline)
echo "Test 1: Simple text message"
curl -s -X POST "$API_URL" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-4-sonnet",
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }' | jq -r '.choices[0].message.content // .error' | head -c 100
echo -e "\n"

# Test 2: Image with text
echo "Test 2: Image with text"
curl -s -X POST "$API_URL" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-4-sonnet",
    "messages": [{
      "role": "user",
      "content": [
        {"type": "text", "text": "What is this?"},
        {"type": "image_url", "image_url": {"url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="}}
      ]
    }],
    "stream": false
  }' | jq -r '.choices[0].message.content // .error' | head -c 100
echo -e "\n"

# Test 3: Multiple images
echo "Test 3: Multiple images"
curl -s -X POST "$API_URL" \
  -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-4-sonnet",
    "messages": [{
      "role": "user",
      "content": [
        {"type": "text", "text": "Compare these:"},
        {"type": "image_url", "image_url": {"url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg=="}},
        {"type": "image_url", "image_url": {"url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="}}
      ]
    }],
    "stream": false
  }' | jq -r '.choices[0].message.content // .error' | head -c 100
echo -e "\n"

echo "=== Image Support Tests Completed ==="
echo "If you see responses above (not errors), image support is working!"
