# JetBrains AI API Configuration
# 客户端API密钥（逗号分隔多个）
CLIENT_API_KEYS=sk-your-custom-key-here

# JetBrains AI 账户配置（逗号分隔多个）
# License ID (如果使用许可证模式)
JETBRAINS_LICENSE_IDS=

# Authorization tokens (如果使用许可证模式，对应license ID)
JETBRAINS_AUTHORIZATIONS=

# JWT tokens (可以是静态JWT或通过license ID获取的JWT)
JETBRAINS_JWTS=your-jwt-here

# Gin mode (debug, release, test)
GIN_MODE=release

# Server port
PORT=7860
REDIS_URL=redis://default:your-redis-password@your-redis-host:6379
# 配置说明:
# 1. CLIENT_API_KEYS: 用于客户端访问API的密钥，支持多个密钥用逗号分隔
# 2. JETBRAINS_LICENSE_IDS: JetBrains许可证ID，如果有多个账户用逗号分隔
# 3. JETBRAINS_AUTHORIZATIONS: 对应许可证的授权token，用于刷新JWT
# 4. JETBRAINS_JWTS: JWT token，可以是静态的或通过许可证获取的
#
# 账户配置示例:
# - 静态JWT方式: 只设置JETBRAINS_JWTS
# - 许可证方式: 设置JETBRAINS_LICENSE_IDS和JETBRAINS_AUTHORIZATIONS
# - 混合方式: 可以同时配置多种方式的账户