package main

import (
	"github.com/bytedance/sonic"
	"log"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/joho/godotenv"
)

const (
	DefaultRequestTimeout = 30 * time.Second
	QuotaCacheTime        = time.Hour
	JWTRefreshTime        = 12 * time.Hour
)

// Global variables
var (
	validClientKeys        = make(map[string]bool)
	jetbrainsAccounts      []JetbrainsAccount
	currentAccountIndex    int
	accountRotationLock    sync.Mutex
	modelsData             ModelsData
	modelsConfig           ModelsConfig
	httpClient             *http.Client
	requestStats           RequestStats
	statsMutex             sync.Mutex
)

func main() {
	// Load .env file
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Initialize storage and load statistics
	if err := initStorage(); err != nil {
		log.Fatalf("Failed to initialize storage: %v", err)
	}
	loadStats()

	// Initialize HTTP client
	transport := &http.Transport{
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     90 * time.Second,
	}
	httpClient = &http.Client{
		Transport: transport,
		Timeout:   DefaultRequestTimeout,
	}

	// Load configuration
	modelsData = loadModels()
	data, err := os.ReadFile("models.json")
	if err == nil {
		sonic.Unmarshal(data, &modelsConfig)
	}
	loadClientAPIKeys()
	loadJetbrainsAccounts()

	// Initialize request-triggered statistics saving
	initRequestTriggeredSaving()

	// Set up graceful shutdown
	setupGracefulShutdown()

	// Start pprof server
	go func() {
		log.Println(http.ListenAndServe("localhost:6060", nil))
	}()

	r := setupRoutes()

	log.Println("Starting JetBrains AI OpenAI Compatible API server...")
	port := getEnvWithDefault("PORT", "7860")

	if err := r.Run(":" + port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}

func setupGracefulShutdown() {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	go func() {
		<-c
		log.Println("Shutdown signal received, saving statistics before exiting...")
		saveStats()
		os.Exit(0)
	}()
}