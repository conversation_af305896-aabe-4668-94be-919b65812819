<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 代理 - 请求统计</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f6f8fa;
            color: #24292e;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 20px 30px;
            border-radius: 8px;
            border: 1px solid #d1d5da;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e1e4e8;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        h1 {
            color: #24292e;
            font-weight: 600;
            margin: 0;
        }
        .refresh-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .auto-refresh-label {
            font-size: 14px;
            color: #586069;
        }
        .refresh-btn {
            background-color: #0366d6;
            color: white;
            border: 1px solid rgba(27, 31, 35, 0.15);
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        .refresh-btn:hover {
            background-color: #005cc5;
        }
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            text-align: center;
            padding: 20px;
            background-color: #f6f8fa;
            border-radius: 8px;
            border: 1px solid #e1e4e8;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #586069;
            font-size: 14px;
            font-weight: 600;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #24292e;
            margin-bottom: 5px;
        }
        .period-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .period-card {
            padding: 20px;
            border: 2px solid #e1e4e8;
            border-radius: 8px;
            background-color: #ffffff;
        }
        .period-card.highlighted {
            border-color: #0366d6;
            background-color: #f6f8fa;
        }
        .period-title {
            color: #0366d6;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .period-stat {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        .section-title {
            color: #0366d6;
            font-size: 18px;
            font-weight: bold;
            margin: 30px 0 15px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e1e4e8;
        }
        th {
            background-color: #f6f8fa;
            font-weight: 600;
        }
        tr:hover {
            background-color: #f6f8fa;
        }
        .status-active {
            color: #28a745;
            font-weight: bold;
        }
        .status-normal {
            color: #28a745;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        .loading {
            color: #586069;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI 代理 - 请求统计</h1>
            <div class="refresh-container">
                <span class="auto-refresh-label">自动刷新：</span>
                <select id="autoRefresh">
                    <option value="0">关闭</option>
                    <option value="30">30秒</option>
                    <option value="60" selected>1分钟</option>
                    <option value="300">5分钟</option>
                </select>
                <button class="refresh-btn" onclick="loadData();">刷新</button>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-overview">
            <div class="stat-card">
                <h3>当前时间</h3>
                <div class="stat-value" id="currentTime">加载中...</div>
            </div>
            <div class="stat-card">
                <h3>当前 QPS</h3>
                <div class="stat-value" id="currentQPS">加载中...</div>
            </div>
            <div class="stat-card">
                <h3>记录总数</h3>
                <div class="stat-value" id="totalRecords">加载中...</div>
            </div>
        </div>

        <!-- 时间段统计 -->
        <div class="period-stats">
            <div class="period-card">
                <div class="period-title">24小时统计</div>
                <div class="period-stat">
                    <span>请求数:</span>
                    <span id="stats24h-requests"><strong>加载中...</strong></span>
                </div>
                <div class="period-stat">
                    <span>成功率:</span>
                    <span id="stats24h-successRate"><strong>加载中...</strong></span>
                </div>
                <div class="period-stat">
                    <span>平均响应时间:</span>
                    <span id="stats24h-avgResponseTime"><strong>加载中...</strong></span>
                </div>
                <div class="period-stat">
                    <span>QPS:</span>
                    <span id="stats24h-qps"><strong>加载中...</strong></span>
                </div>
            </div>
            
            <div class="period-card">
                <div class="period-title">7天统计</div>
                <div class="period-stat">
                    <span>请求数:</span>
                    <span id="stats7d-requests"><strong>加载中...</strong></span>
                </div>
                <div class="period-stat">
                    <span>成功率:</span>
                    <span id="stats7d-successRate"><strong>加载中...</strong></span>
                </div>
                <div class="period-stat">
                    <span>平均响应时间:</span>
                    <span id="stats7d-avgResponseTime"><strong>加载中...</strong></span>
                </div>
                <div class="period-stat">
                    <span>QPS:</span>
                    <span id="stats7d-qps"><strong>加载中...</strong></span>
                </div>
            </div>
            
            <div class="period-card">
                <div class="period-title">30天统计</div>
                <div class="period-stat">
                    <span>请求数:</span>
                    <span id="stats30d-requests"><strong>加载中...</strong></span>
                </div>
                <div class="period-stat">
                    <span>成功率:</span>
                    <span id="stats30d-successRate"><strong>加载中...</strong></span>
                </div>
                <div class="period-stat">
                    <span>平均响应时间:</span>
                    <span id="stats30d-avgResponseTime"><strong>加载中...</strong></span>
                </div>
                <div class="period-stat">
                    <span>QPS:</span>
                    <span id="stats30d-qps"><strong>加载中...</strong></span>
                </div>
            </div>
        </div>

        <!-- Token 配额信息 -->
        <div class="section-title">所有 Token 配额信息</div>
        <table>
            <thead>
                <tr>
                    <th>Token名称</th>
                    <th>许可证</th>
                    <th>已使用</th>
                    <th>最大配额</th>
                    <th>使用率</th>
                    <th>有效期至</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody id="tokensTable">
                <tr>
                    <td colspan="7" class="loading">加载中...</td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        let autoRefreshInterval;
        
        // 获取数据
        async function loadData() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();
                
                // 更新概览数据
                document.getElementById('currentTime').textContent = data.currentTime;
                document.getElementById('currentQPS').textContent = parseFloat(data.currentQPS).toFixed(3);
                document.getElementById('totalRecords').textContent = data.totalRecords;
                
                // 更新24小时统计
                document.getElementById('stats24h-requests').innerHTML = '<strong>' + data.stats24h.requests + '</strong>';
                document.getElementById('stats24h-successRate').innerHTML = '<strong>' + data.stats24h.successRate.toFixed(2) + '%</strong>';
                document.getElementById('stats24h-avgResponseTime').innerHTML = '<strong>' + (data.stats24h.avgResponseTime / 1000).toFixed(2) + ' 秒</strong>';
                document.getElementById('stats24h-qps').innerHTML = '<strong>' + data.stats24h.qps.toFixed(4) + '</strong>';
                
                // 更新7天统计
                document.getElementById('stats7d-requests').innerHTML = '<strong>' + data.stats7d.requests + '</strong>';
                document.getElementById('stats7d-successRate').innerHTML = '<strong>' + data.stats7d.successRate.toFixed(2) + '%</strong>';
                document.getElementById('stats7d-avgResponseTime').innerHTML = '<strong>' + (data.stats7d.avgResponseTime / 1000).toFixed(2) + ' 秒</strong>';
                document.getElementById('stats7d-qps').innerHTML = '<strong>' + data.stats7d.qps.toFixed(4) + '</strong>';
                
                // 更新30天统计
                document.getElementById('stats30d-requests').innerHTML = '<strong>' + data.stats30d.requests + '</strong>';
                document.getElementById('stats30d-successRate').innerHTML = '<strong>' + data.stats30d.successRate.toFixed(2) + '%</strong>';
                document.getElementById('stats30d-avgResponseTime').innerHTML = '<strong>' + (data.stats30d.avgResponseTime / 1000).toFixed(2) + ' 秒</strong>';
                document.getElementById('stats30d-qps').innerHTML = '<strong>' + data.stats30d.qps.toFixed(4) + '</strong>';
                
                // 更新Token配额表
                const tokensTable = document.getElementById('tokensTable');
                tokensTable.innerHTML = '';
                data.tokensInfo.forEach(token => {
                    // 格式化有效期显示为 YYYY-MM-DD HH:mm:ss 格式
                    let formattedExpiryDate = token.expiryDate;
                    
                    const row = tokensTable.insertRow();
                    row.innerHTML = `
                        <td>${token.name}</td>
                        <td>${token.license}</td>
                        <td>${token.used.toFixed(3)}</td>
                        <td>${token.total.toFixed(0)}</td>
                        <td>${token.usageRate.toFixed(2)}%</td>
                        <td>${formattedExpiryDate}</td>
                        <td><span class="status-active">${token.status}</span></td>
                    `;
                });
                
            } catch (error) {
                console.error('Failed to load data:', error);
            }
        }
        
        // 自动刷新功能
        function setupAutoRefresh() {
            const select = document.getElementById('autoRefresh');
            const interval = parseInt(select.value) * 1000;
            
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
            
            if (interval > 0) {
                autoRefreshInterval = setInterval(loadData, interval);
            }
        }

        // 更新当前时间
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.getFullYear() + '-' + 
                             String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                             String(now.getDate()).padStart(2, '0') + ' ' + 
                             String(now.getHours()).padStart(2, '0') + ':' + 
                             String(now.getMinutes()).padStart(2, '0') + ':' + 
                             String(now.getSeconds()).padStart(2, '0');
            document.getElementById('currentTime').textContent = timeString;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadData(); // 立即加载数据
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000); // 每秒更新时间
            
            const autoRefreshSelect = document.getElementById('autoRefresh');
            autoRefreshSelect.addEventListener('change', setupAutoRefresh);
            setupAutoRefresh(); // 初始化自动刷新
        });
    </script>
</body>
</html>